# Verify Payments | Paystack Developer Documentation

# Verify Payments

In A Nutshell

##### In a nutshell

The Verify Transaction API allows you confirm the status of a customer's transaction.

## [](#transaction-statuses)Transaction statuses

Webhooks are the preferred option for confirming a transaction status, but we currently send webhook events for just successful transactions. However, a transaction can have the following statuses:

Status

Meaning

`abandoned`

The customer has not completed the transaction.

`failed`

The transaction failed. For more information on why, refer to the message/gateway response.

`ongoing`

The customer is currently trying to carry out an action to complete the transaction. This can get returned when we're waiting on the customer to enter an otp or to make a transfer (for a pay with transfer transaction).

`pending`

The transaction is currently in progress.

`processing`

Same as `pending`, but for direct debit transactions.

`queued`

The transaction has been queued to be processed later. Only possible on bulk charge transactions.

`reversed`

The transaction was reversed. This could mean the transaction was refunded, or a chargeback was successfully logged for this transaction.

`success`

The transaction was successfully processed.

## [](#verify-a-transaction)Verify a transaction

You do this by making a `GET` request to the [Verify TransactionAPI](https://paystack.com/docs/api/transaction#verify) endpoint from your server using your transaction reference. This is dependent on the method you used to initialize the transaction.

### [](#from-popup-or-mobile-sdks)From Popup or Mobile SDKs

You'll have to send the reference to your server, then from your server you call the verify endpoint.

### [](#from-the-redirect-api)From the Redirect API

You initiate this request from your callback URL. The transaction reference is returned as a query parameter to your callback URL.

##### Helpful Tip

If you offer digital value like airtime, wallet top-up, digital credit, etc, always confirm that you have not already delivered value for that transaction to avoid double fulfillments, especially, if you also use webhooks.

Here's a code sample for verifying transactions:

cURLNodePHP

Show Response

1#!/bin/sh

2curl https://api.paystack.co/transaction/verify/:reference

3\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

4\-X GET

1{

2  "status": true,

3  "message": "Verification successful",

4  "data": {

5    "id": 4099260516,

6    "domain": "test",

7    "status": "success",

8    "reference": "re4lyvq3s3",

9    "receipt\_number": null,

10    "amount": 40333,

11    "message": null,

12    "gateway\_response": "Successful",

13    "paid\_at": "2024-08-22T09:15:02.000Z",

14    "created\_at": "2024-08-22T09:14:24.000Z",

15    "channel": "card",

16    "currency": "NGN",

17    "ip\_address": "*************",

18    "metadata": "",

19    "log": {

20      "start\_time": 1724318098,

21      "time\_spent": 4,

22      "attempts": 1,

23      "errors": 0,

24      "success": true,

25      "mobile": false,

26      "input": \[\],

27      "history": \[

28        {

29          "type": "action",

30          "message": "Attempted to pay with card",

31          "time": 3

32        },

33        {

34          "type": "success",

35          "message": "Successfully paid with card",

36          "time": 4

37        }

38      \]

39    },

40    "fees": 10283,

41    "fees\_split": null,

42    "authorization": {

43      "authorization\_code": "AUTH\_uh8bcl3zbn",

44      "bin": "408408",

45      "last4": "4081",

46      "exp\_month": "12",

47      "exp\_year": "2030",

48      "channel": "card",

49      "card\_type": "visa ",

50      "bank": "TEST BANK",

51      "country\_code": "NG",

52      "brand": "visa",

53      "reusable": true,

54      "signature": "SIG\_yEXu7dLBeqG0kU7g95Ke",

55      "account\_name": null

56    },

57    "customer": {

58      "id": *********,

59      "first\_name": null,

60      "last\_name": null,

61      "email": "<EMAIL>",

62      "customer\_code": "CUS\_1rkzaqsv4rrhqo6",

63      "phone": null,

64      "metadata": null,

65      "risk\_action": "default",

66      "international\_format\_phone": null

67    },

68    "plan": null,

69    "split": {},

70    "order\_id": null,

71    "paidAt": "2024-08-22T09:15:02.000Z",

72    "createdAt": "2024-08-22T09:14:24.000Z",

73    "requested\_amount": 30050,

74    "pos\_transaction\_data": null,

75    "source": null,

76    "fees\_breakdown": null,

77    "connect": null,

78    "transaction\_date": "2024-08-22T09:14:24.000Z",

79    "plan\_object": {},

80    "subaccount": {}

81  }

82}

##### Warning

The API response has a status key `response.status` indicating the status of the API call. This is **not** the status of the transaction. The status of the transaction is in the `data` object in the verify API response, i.e `response.data.status`. Learn more about [Paystack API format](https://paystack.com/docs/api).

## [](#charge-returning-users)Charge returning Users

The `verify` response also returns information about the payment instrument that the user paid with in the `data.authorization` object. If the channel is `card`, then you can store the `authorization_code` for that card against that user, and use that charge the user for subsequent transaction. Learn more about [recurring charges](/docs/payments/recurring-charges/).