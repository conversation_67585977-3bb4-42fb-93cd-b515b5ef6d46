# Payments | Paystack Developer Documentation

# Payments

Learn how to receive fast and secure payments with Paystack.

## [](#getting-started)Getting started

[

#### Accept Payments

Customers can pay you using any of our supported payment methods.



](/docs/payments/accept-payments/)[

#### Recurring Charges

Charge customers on a recurring basis with subscriptions or authorizations.



](/docs/payments/recurring-charges/)[

#### Split Payments

Automatically split your transaction payouts into multiple accounts.



](/docs/payments/split-payments/)[

#### Libraries and Plugins

Integrate to Paystack with plugins and libraries of your preferred language.



](/docs/libraries-and-plugins/)

## [](#explore-demos)Explore demos

We’ve put together simple projects to demonstrate how the Paystack API works for various financial services. [Explore all demos](https://github.com/PaystackOSS/) or start with the most popular ones below.

##### Gift Store

[PaystackOSS/sample-gift-store](https://github.com/PaystackOSS/sample-gift-store)

APIS USED

---

-   [Accept Payments](/docs/payments/accept-payments/)
-   [Verify Payments](/docs/payments/verify-payments/)

Vue

##### Movie Ticket

[PaystackOSS/sample-movie-ticket](https://github.com/PaystackOSS/sample-movie-ticket)

APIS USED

---

-   [Accept Payments](/docs/payments/accept-payments/)
-   [Verify Payments](/docs/payments/verify-payments/)

Android