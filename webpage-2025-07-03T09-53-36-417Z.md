# Partial Debits | Paystack Developer Documentation

# Partial Debits

Increase your revenue by recovering funds that would have otherwise been lost

## [](#introduction)Introduction

With this feature, when an attempt to charge a card fails with insufficient funds, we will automatically retry with the maximum amount that can be charged. For example, if you attempt to charge someone NGN 10,000 but they only have NGN 4,000 in their account, historically, the transaction will fail with `insufficient funds`, but with partial debits, we will charge about NGN 3,900,

##### Allowed Cards

Partial debit is only available for `Mastercard` and `Verve` at the moment

## [](#who-can-use-this)Who can use this?

This feature is only available on request. You will gain access once our reviews team approves it. Please reach out to `<EMAIL>` to request access.

## [](#how-does-it-work)How does it work?

You can only charge existing authorizations with this feature. There are 2 ways to use it. Either through the [Partial DebitAPI](https://paystack.com/docs/api/transaction#partial-debit) endpoint or our [Bulk ChargeAPI](https://paystack.com/docs/api/bulk-charge#initiate) endpoint.

##### Using the Bulk Charge endpoint

When using the bulk charge endpoint you need to pass the flag `"attempt_partial_debit" : true`

## [](#verifying-transactions)Verifying Transactions

When verifying transactions (or implementing webhooks) that involve Partial Debits, there are two types of amounts that are returned:

Param

Description

amount

This is the amount, in the subunit of the [supported currency](/api#supported-currency), that the customer was charged

requested\_amount

This is the amount, [supported currency](/api#supported-currency), you intended to charge.